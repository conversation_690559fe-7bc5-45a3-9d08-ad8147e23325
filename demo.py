#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度条演示程序
展示各种进度条的使用方法
"""

import time
import random
from progress_bar import SimpleProgressBar, AdvancedProgressBar, SpinnerProgressBar, progress_bar_context


def demo_simple_progress_bar():
    """演示简单进度条"""
    print("=" * 60)
    print("1. 简单进度条演示")
    print("=" * 60)
    
    # 基本用法
    print("\n基本进度条:")
    pbar = SimpleProgressBar(total=50, width=40)
    for i in range(51):
        pbar.set_progress(i)
        time.sleep(0.05)
    
    # 自定义样式
    print("\n自定义样式进度条:")
    pbar2 = SimpleProgressBar(total=30, width=30, fill_char='#', empty_char='-')
    for i in range(31):
        pbar2.update()
        time.sleep(0.08)
    
    # 使用不同字符
    print("\n使用不同字符的进度条:")
    pbar3 = SimpleProgressBar(total=25, width=35, fill_char='▓', empty_char='▒')
    for i in range(26):
        pbar3.update()
        time.sleep(0.1)


def demo_advanced_progress_bar():
    """演示高级进度条"""
    print("\n\n" + "=" * 60)
    print("2. 高级进度条演示")
    print("=" * 60)
    
    # 带描述和时间信息
    print("\n带描述和时间信息的进度条:")
    pbar = AdvancedProgressBar(total=100, desc="下载文件", width=40)
    for i in range(101):
        pbar.update()
        # 模拟不均匀的处理速度
        time.sleep(random.uniform(0.01, 0.05))
    
    # 批处理演示
    print("\n批处理演示:")
    files = ["file1.txt", "file2.txt", "file3.txt", "file4.txt", "file5.txt"]
    pbar2 = AdvancedProgressBar(total=len(files), desc="处理文件", width=30)
    for i, filename in enumerate(files):
        print(f"\n正在处理: {filename}")
        # 模拟文件处理
        for j in range(20):
            time.sleep(0.02)
        pbar2.update()
    
    # 数据分析演示
    print("\n数据分析演示:")
    pbar3 = AdvancedProgressBar(total=200, desc="分析数据", width=50, show_eta=True, show_speed=True)
    for i in range(201):
        pbar3.update()
        # 模拟数据处理的变化速度
        if i < 50:
            time.sleep(0.01)  # 开始较快
        elif i < 150:
            time.sleep(0.03)  # 中间较慢
        else:
            time.sleep(0.01)  # 结束时又变快


def demo_spinner_progress_bar():
    """演示旋转进度条"""
    print("\n\n" + "=" * 60)
    print("3. 旋转进度条演示")
    print("=" * 60)
    
    # 基本旋转进度条
    print("\n基本旋转进度条:")
    spinner = SpinnerProgressBar("连接服务器")
    for i in range(50):
        spinner.update()
        time.sleep(0.1)
    spinner.finish("连接成功!")
    
    # 自定义旋转字符
    print("\n自定义旋转字符:")
    spinner2 = SpinnerProgressBar("加载数据", spinner_chars="|/-\\")
    for i in range(30):
        spinner2.update()
        time.sleep(0.15)
    spinner2.finish("数据加载完成!")
    
    # 另一种旋转样式
    print("\n另一种旋转样式:")
    spinner3 = SpinnerProgressBar("初始化系统", spinner_chars="⣾⣽⣻⢿⡿⣟⣯⣷")
    for i in range(40):
        spinner3.update()
        time.sleep(0.08)
    spinner3.finish("系统初始化完成!")


def demo_context_manager():
    """演示上下文管理器用法"""
    print("\n\n" + "=" * 60)
    print("4. 上下文管理器演示")
    print("=" * 60)
    
    # 使用上下文管理器
    print("\n使用上下文管理器:")
    with progress_bar_context(80, "训练模型", width=45) as pbar:
        for epoch in range(80):
            # 模拟训练过程
            time.sleep(0.03)
            pbar.update()
    
    # 嵌套进度条演示
    print("\n嵌套处理演示:")
    datasets = ["训练集", "验证集", "测试集"]
    
    with progress_bar_context(len(datasets), "处理数据集", width=40) as main_pbar:
        for dataset_name in datasets:
            print(f"\n处理 {dataset_name}:")
            
            # 内层进度条
            with progress_bar_context(50, f"  处理{dataset_name}", width=35) as sub_pbar:
                for i in range(50):
                    time.sleep(0.02)
                    sub_pbar.update()
            
            main_pbar.update()


def demo_real_world_examples():
    """真实世界的使用示例"""
    print("\n\n" + "=" * 60)
    print("5. 真实世界使用示例")
    print("=" * 60)
    
    # 文件复制模拟
    print("\n文件复制模拟:")
    file_size = 1024 * 1024  # 1MB
    chunk_size = 8192  # 8KB chunks
    total_chunks = file_size // chunk_size
    
    pbar = AdvancedProgressBar(total_chunks, desc="复制文件", width=40)
    for chunk in range(total_chunks + 1):
        # 模拟读写操作
        time.sleep(0.01)
        pbar.update()
    
    # 网络下载模拟
    print("\n网络下载模拟:")
    download_size = 50 * 1024 * 1024  # 50MB
    downloaded = 0
    
    pbar2 = AdvancedProgressBar(download_size, desc="下载", width=45)
    while downloaded < download_size:
        # 模拟网络波动
        chunk_size = random.randint(1024, 10240)  # 1-10KB
        downloaded = min(downloaded + chunk_size, download_size)
        pbar2.set_progress(downloaded)
        time.sleep(0.01)


def main():
    """主函数"""
    print("Python 文字进度条演示程序")
    print("作者: AI Assistant")
    print("=" * 60)
    
    try:
        demo_simple_progress_bar()
        demo_advanced_progress_bar()
        demo_spinner_progress_bar()
        demo_context_manager()
        demo_real_world_examples()
        
        print("\n\n" + "=" * 60)
        print("演示完成! 🎉")
        print("=" * 60)
        print("\n使用说明:")
        print("1. SimpleProgressBar - 简单的进度条，适合基本需求")
        print("2. AdvancedProgressBar - 高级进度条，显示速度和预计时间")
        print("3. SpinnerProgressBar - 旋转进度条，适合不确定进度的任务")
        print("4. progress_bar_context - 上下文管理器，自动处理完成状态")
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n\n发生错误: {e}")


if __name__ == "__main__":
    main()
