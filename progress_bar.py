#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文字进度条模块
提供多种样式的文字进度条实现
"""

import sys
import time
from typing import Optional, Union


class SimpleProgressBar:
    """简单的文字进度条"""
    
    def __init__(self, total: int, width: int = 50, fill_char: str = '█', empty_char: str = '░'):
        """
        初始化进度条
        
        Args:
            total: 总步数
            width: 进度条宽度（字符数）
            fill_char: 填充字符
            empty_char: 空白字符
        """
        self.total = total
        self.width = width
        self.fill_char = fill_char
        self.empty_char = empty_char
        self.current = 0
        
    def update(self, step: int = 1):
        """更新进度条"""
        self.current = min(self.current + step, self.total)
        self.display()
        
    def set_progress(self, current: int):
        """设置当前进度"""
        self.current = min(max(current, 0), self.total)
        self.display()
        
    def display(self):
        """显示进度条"""
        if self.total == 0:
            percent = 100
            filled_length = self.width
        else:
            percent = (self.current / self.total) * 100
            filled_length = int(self.width * self.current // self.total)
        
        bar = self.fill_char * filled_length + self.empty_char * (self.width - filled_length)
        
        # 使用 \r 回到行首，实现原地更新
        sys.stdout.write(f'\r[{bar}] {percent:.1f}% ({self.current}/{self.total})')
        sys.stdout.flush()
        
        if self.current >= self.total:
            print()  # 完成后换行


class AdvancedProgressBar:
    """高级文字进度条，支持更多功能"""
    
    def __init__(self, total: int, width: int = 50, desc: str = "", 
                 fill_char: str = '█', empty_char: str = '░', 
                 show_eta: bool = True, show_speed: bool = True):
        """
        初始化高级进度条
        
        Args:
            total: 总步数
            width: 进度条宽度
            desc: 描述文字
            fill_char: 填充字符
            empty_char: 空白字符
            show_eta: 是否显示预计剩余时间
            show_speed: 是否显示处理速度
        """
        self.total = total
        self.width = width
        self.desc = desc
        self.fill_char = fill_char
        self.empty_char = empty_char
        self.show_eta = show_eta
        self.show_speed = show_speed
        
        self.current = 0
        self.start_time = time.time()
        self.last_update_time = self.start_time
        
    def update(self, step: int = 1):
        """更新进度条"""
        self.current = min(self.current + step, self.total)
        self.last_update_time = time.time()
        self.display()
        
    def set_progress(self, current: int):
        """设置当前进度"""
        self.current = min(max(current, 0), self.total)
        self.last_update_time = time.time()
        self.display()
        
    def display(self):
        """显示高级进度条"""
        if self.total == 0:
            percent = 100
            filled_length = self.width
        else:
            percent = (self.current / self.total) * 100
            filled_length = int(self.width * self.current // self.total)
        
        bar = self.fill_char * filled_length + self.empty_char * (self.width - filled_length)
        
        # 构建显示字符串
        display_parts = []
        
        if self.desc:
            display_parts.append(self.desc)
            
        display_parts.append(f'[{bar}]')
        display_parts.append(f'{percent:.1f}%')
        display_parts.append(f'({self.current}/{self.total})')
        
        # 计算时间相关信息
        elapsed_time = time.time() - self.start_time
        
        if self.show_speed and elapsed_time > 0:
            speed = self.current / elapsed_time
            if speed > 0:
                display_parts.append(f'{speed:.1f}it/s')
        
        if self.show_eta and self.current > 0 and self.current < self.total:
            remaining = self.total - self.current
            eta_seconds = remaining * elapsed_time / self.current
            eta_str = self._format_time(eta_seconds)
            display_parts.append(f'ETA: {eta_str}')
        
        # 输出进度条
        output = ' '.join(display_parts)
        sys.stdout.write(f'\r{output}')
        sys.stdout.flush()
        
        if self.current >= self.total:
            total_time = self._format_time(elapsed_time)
            print(f' - 完成! 总用时: {total_time}')
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        if seconds < 60:
            return f'{seconds:.1f}s'
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f'{minutes}m{secs}s'
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f'{hours}h{minutes}m'


class SpinnerProgressBar:
    """旋转进度条（适用于不确定进度的任务）"""
    
    def __init__(self, desc: str = "处理中", spinner_chars: str = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"):
        """
        初始化旋转进度条
        
        Args:
            desc: 描述文字
            spinner_chars: 旋转字符序列
        """
        self.desc = desc
        self.spinner_chars = spinner_chars
        self.current_char_index = 0
        self.start_time = time.time()
        
    def update(self):
        """更新旋转进度条"""
        elapsed = time.time() - self.start_time
        char = self.spinner_chars[self.current_char_index % len(self.spinner_chars)]
        
        sys.stdout.write(f'\r{char} {self.desc}... ({elapsed:.1f}s)')
        sys.stdout.flush()
        
        self.current_char_index += 1
        
    def finish(self, message: str = "完成!"):
        """结束旋转进度条"""
        elapsed = time.time() - self.start_time
        print(f'\r✓ {message} ({elapsed:.1f}s)')


def progress_bar_context(total: int, desc: str = "", **kwargs):
    """
    进度条上下文管理器
    
    Usage:
        with progress_bar_context(100, "处理数据") as pbar:
            for i in range(100):
                # 做一些工作
                time.sleep(0.01)
                pbar.update()
    """
    class ProgressBarContext:
        def __init__(self, total, desc, **kwargs):
            self.pbar = AdvancedProgressBar(total, desc=desc, **kwargs)
            
        def __enter__(self):
            return self.pbar
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            if exc_type is None and self.pbar.current < self.pbar.total:
                self.pbar.set_progress(self.pbar.total)
    
    return ProgressBarContext(total, desc, **kwargs)


if __name__ == "__main__":
    # 简单测试
    print("测试简单进度条:")
    pbar = SimpleProgressBar(100)
    for i in range(101):
        pbar.set_progress(i)
        time.sleep(0.02)
    
    print("\n测试高级进度条:")
    pbar2 = AdvancedProgressBar(50, desc="数据处理")
    for i in range(51):
        pbar2.set_progress(i)
        time.sleep(0.05)
